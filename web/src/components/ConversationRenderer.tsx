import React from 'react';
import { MessageRenderer } from './MessageRenderer';
import './ConversationRenderer.css';

// 参考 VSCode 插件的 LocalMessage 类型
interface LocalMessage {
  ts: number;
  role?: "user";
  sessionId: string;
  chatId?: string;
  type: "ask" | "say";
  ask?: string;
  say?: string;
  text?: string;
  partial?: boolean;
  lastCheckpointHash?: string;
  isCheckpointCheckedOut?: boolean;
  conversationHistoryIndex?: number;
  conversationHistoryDeletedRange?: [number, number];
}

interface ConversationRendererProps {
  localMessages: LocalMessage[];
  isStreaming?: boolean;
}

// 参考 VSCode 插件的 isHumanMessage 逻辑判断用户消息
const isHumanMessage = (message: LocalMessage): boolean => {
  return message.type === "say" && message.say === "text" && message.role === "user";
};

// 参考 VSCode 插件的对话分组逻辑
const groupMessagesIntoConversations = (messages: LocalMessage[]) => {
  const conversations: { humanMessage: LocalMessage; taskRows: LocalMessage[] }[] = [];
  let currentConversation: { humanMessage: LocalMessage; taskRows: LocalMessage[] } | null = null;

  for (const message of messages) {
    // 如果是用户消息，开始新的对话
    if (isHumanMessage(message)) {
      if (currentConversation) {
        conversations.push(currentConversation);
      }
      currentConversation = {
        humanMessage: message,
        taskRows: []
      };
    } else if (currentConversation) {
      // 如果是助手消息，添加到当前对话的任务行
      currentConversation.taskRows.push(message);
    } else {
      // 如果没有当前对话但有助手消息，创建一个空的用户消息
      currentConversation = {
        humanMessage: {
          ts: message.ts - 1,
          sessionId: message.sessionId,
          type: 'ask',
          role: 'user',
          text: '',
        },
        taskRows: [message]
      };
    }
  }

  if (currentConversation) {
    conversations.push(currentConversation);
  }

  return conversations;
};

export const ConversationRenderer: React.FC<ConversationRendererProps> = ({ 
  localMessages, 
  isStreaming 
}) => {
  const conversations = groupMessagesIntoConversations(localMessages);

  if (conversations.length === 0) {
    return (
      <div className="conversation-empty">
        <div className="welcome-container">
          <h1>DeepSearch 问答</h1>
          <p>有问题尽管问我，我会尽力帮助你！</p>
        </div>
      </div>
    );
  }

  return (
    <div className="conversation-container">
      {conversations.map((conversation, index) => {
        const isLastConversation = index === conversations.length - 1;
        
        return (
          <div 
            key={conversation.humanMessage.ts} 
            className={`conversation ${isLastConversation ? 'last-conversation' : ''}`}
          >
            {/* 用户消息 */}
            {conversation.humanMessage.text && (
              <div className="human-message">
                <div className="message-avatar">
                  <span className="user-icon">👤</span>
                </div>
                <div className="message-bubble user-bubble">
                  <div className="message-content">
                    {conversation.humanMessage.text}
                  </div>
                  <div className="message-time">
                    {new Date(conversation.humanMessage.ts).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            )}

            {/* 助手消息 */}
            {conversation.taskRows.length > 0 && (
              <div className="assistant-messages">
                <div className="message-avatar">
                  <span className="assistant-icon">🤖</span>
                </div>
                <div className="message-bubble assistant-bubble">
                  {conversation.taskRows.map((message, taskIndex) => {
                    const isLastMessage = isLastConversation && taskIndex === conversation.taskRows.length - 1;
                    
                    return (
                      <div key={message.ts} className="task-row">
                        <MessageRenderer 
                          localMessage={message} 
                          isStreaming={isLastMessage && isStreaming}
                        />
                      </div>
                    );
                  })}
                  
                  {/* 流式状态指示器 */}
                  {isLastConversation && isStreaming && (
                    <div className="streaming-status">
                      <div className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <span className="status-text">AI 正在思考...</span>
                    </div>
                  )}
                  
                  <div className="message-time">
                    {conversation.taskRows.length > 0 && 
                      new Date(conversation.taskRows[conversation.taskRows.length - 1].ts).toLocaleTimeString()
                    }
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
