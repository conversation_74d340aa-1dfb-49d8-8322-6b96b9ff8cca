import React, { useState, useRef, useEffect } from 'react';
import { Routes, Route, useParams, useNavigate } from 'react-router-dom';
import { io, Socket } from 'socket.io-client';
import { ConversationRenderer } from './components/ConversationRenderer';
import './App.css';

// 参考 VSCode 插件的消息类型定义
interface LocalMessage {
  ts: number;
  role?: "user";
  sessionId: string;
  chatId?: string;
  type: "ask" | "say";
  ask?: string;
  say?: string;
  text?: string;
  partial?: boolean;
  lastCheckpointHash?: string;
  isCheckpointCheckedOut?: boolean;
  conversationHistoryIndex?: number;
  conversationHistoryDeletedRange?: [number, number];
}

// 参考 VSCode 插件的状态类型定义
interface ComposerState {
  localMessages: LocalMessage[];
  workspaceUri: string;
  sessionId: string;
  currentTaskInterrupted: boolean;
  indeterminatedWorkingSetEffects: any[];
  isCurrentWorkspaceSession: boolean;
  editingMessageTs?: number;
  currentMessageTs?: number;
  userPreferredModel?: string;
  localServiceConnectionLost: boolean;
  indexed?: any;
  cachePathInfos?: any[];
}

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  createdAt: number;
}

interface SessionSummary {
  id: string;
  name: string;
  messageCount: number;
  createdAt: number;
  updatedAt: number;
}

const API_URL = 'http://localhost:3001';

// 简单的文本截断函数
const truncateText = (text: string, maxLength = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// 参考 VSCode 插件的 isHumanMessage 逻辑判断用户消息
const isHumanMessage = (message: LocalMessage): boolean => {
  return message.type === "say" && message.say === "text" && message.role === "user";
};

// 参考 VSCode 插件的消息转换逻辑，正确判断消息角色
const convertLocalMessageToMessage = (localMsg: LocalMessage): Message => {
  return {
    id: localMsg.ts?.toString() || Date.now().toString(),
    content: localMsg.text || '',
    role: isHumanMessage(localMsg) ? 'user' : 'assistant',
    createdAt: localMsg.ts || Date.now(),
  };
};

const convertLocalMessagesToMessages = (localMessages: LocalMessage[]): Message[] => {
  return localMessages.map(convertLocalMessageToMessage);
};

// 参考 VSCode 插件的流式状态判断逻辑
const EndSayMessage: string[] = ["completion_result", "error"];

// 会话组件 - 处理具体的会话逻辑
function SessionView() {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();

  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [sessions, setSessions] = useState<SessionSummary[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string>(sessionId || '');
  const currentSessionIdRef = useRef<string>(sessionId || '');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  // 参考 VSCode 插件的状态管理
  const [composerState, setComposerState] = useState<ComposerState>({
    localMessages: [],
    workspaceUri: "",
    sessionId: "",
    currentTaskInterrupted: false,
    indeterminatedWorkingSetEffects: [],
    isCurrentWorkspaceSession: true,
    editingMessageTs: undefined,
    currentMessageTs: undefined,
    userPreferredModel: "claude3",
    localServiceConnectionLost: false,
  });

  const [isStreaming, setIsStreaming] = useState(false);

  // 参考 VSCode 插件的流式状态判断逻辑
  const updateStreamingStatus = (localMessages: LocalMessage[]) => {
    const lastMessage = localMessages.at(-1);

    if (!localMessages.length || !lastMessage) {
      setIsStreaming(false);
      return;
    }

    if (lastMessage.type === "say" && lastMessage.say && !EndSayMessage.includes(lastMessage.say)) {
      setIsStreaming(true);
      return;
    }
    const isLastMessagePartial = lastMessage.partial === true;
    setIsStreaming(isLastMessagePartial);
  };

  // 获取会话列表
  const fetchSessions = async () => {
    try {
      const response = await fetch(`${API_URL}/sessions`);
      const data = await response.json();
      setSessions(data);
    } catch (error) {
      console.error('获取会话失败', error);
    }
  };

  // 创建新会话
  const createNewSession = async () => {
    try {
      const response = await fetch(`${API_URL}/sessions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: '新对话 ' + new Date().toLocaleString()
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const session = await response.json();
      console.log('Created new session:', session);
      setCurrentSessionId(session.id);
      currentSessionIdRef.current = session.id; // 同步更新 ref
      setMessages([]);
      await fetchSessions();
      // 跳转到新会话的路由
      navigate(`/${session.id}`);
    } catch (error) {
      console.error('创建会话失败', error);
      alert('创建会话失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 切换会话
  const switchSession = async (sessionId: string) => {
    setCurrentSessionId(sessionId);
    currentSessionIdRef.current = sessionId; // 同步更新 ref
    // 跳转到会话的路由
    navigate(`/${sessionId}`);
    try {
      const response = await fetch(`${API_URL}/sessions/${sessionId}`);
      const data = await response.json();

      // 优先使用 localMessages 更新 composerState
      if (data.localMessages && data.localMessages.length > 0) {
        // 清理历史消息的 partial 状态，避免显示多余的光标
        const cleanedLocalMessages = data.localMessages.map((msg: LocalMessage) => ({
          ...msg,
          partial: false, // 历史消息都不应该是 partial 状态
        }));

        setComposerState(prevState => ({
          ...prevState,
          localMessages: cleanedLocalMessages,
          sessionId: sessionId,
          workspaceUri: data.workspaceUri || prevState.workspaceUri,
          currentMessageTs: data.currentMessageTs,
          editingMessageTs: data.editingMessageTs,
          indexed: data.indexed,
        }));

        // 清空传统的 messages，让 ConversationRenderer 来渲染
        setMessages([]);
        // 清理流式状态
        setIsStreaming(false);
      } else {
        // 如果没有 localMessages，使用传统的 messages 作为 fallback
        setMessages(data.messages || []);
        // 清空 localMessages
        setComposerState(prevState => ({
          ...prevState,
          localMessages: [],
          sessionId: sessionId,
        }));
        // 清理流式状态
        setIsStreaming(false);
      }
    } catch (error) {
      console.error('获取会话消息失败', error);
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!question.trim()) return;

    setLoading(true);

    // 先添加用户消息到UI
    const userMessage: Message = {
      id: 'temp-' + Date.now(),
      content: question,
      role: 'user',
      createdAt: Date.now(),
    };

    setMessages((prev) => [...prev, userMessage]);
    const messageContent = question; // 保存消息内容
    setQuestion('');

    try {
      // 使用智能聊天端点，它会自动处理会话创建
      const response = await fetch(`${API_URL}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: messageContent,
          sessionId: currentSessionId || undefined
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // 如果返回了新的会话ID，更新当前会话ID和路由
      if (data.sessionId && data.sessionId !== currentSessionId) {
        console.log('Updating session ID:', data.sessionId);
        setCurrentSessionId(data.sessionId);
        currentSessionIdRef.current = data.sessionId; // 同步更新 ref
        // 更新路由到新的会话
        navigate(`/${data.sessionId}`);
      }

      // 添加助手回复
      setMessages((prev) => [...prev, {
        id: data.id,
        content: data.content,
        role: data.role,
        createdAt: data.createdAt,
      }]);

      // 刷新会话列表
      fetchSessions();
    } catch (error) {
      console.error('发送消息失败', error);
      setMessages((prev) => [
        ...prev,
        {
          id: 'error-' + Date.now(),
          content: '发送消息失败，请稍后重试。错误: ' + (error instanceof Error ? error.message : String(error)),
          role: 'assistant',
          createdAt: Date.now(),
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 自动调整textarea高度
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = 
        Math.min(textareaRef.current.scrollHeight, 150) + 'px';
    }
  };

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 初始化 WebSocket 连接
  useEffect(() => {
    const newSocket = io(`${API_URL}/composer`, {
      transports: ['websocket', 'polling'],
    });

    newSocket.on('connect', () => {
      console.log('WebSocket 连接成功');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('WebSocket 连接断开');
      setIsConnected(false);
    });

    // 监听状态更新事件，参考 VSCode 插件的 COMPOSER_STATE_UPDATE
    newSocket.on('composerStateUpdate', (event) => {
      console.log('收到状态更新:', event);
      console.log('当前会话ID (ref):', currentSessionIdRef.current, '事件会话ID:', event.sessionId);
      if (event.sessionId === currentSessionIdRef.current && event.data) {
        // 更新 composer 状态
        setComposerState(prevState => ({
          ...prevState,
          ...event.data,
          sessionId: event.sessionId,
        }));

        // 将 localMessages 转换为前端的 Message 格式并渲染
        if (event.data.localMessages) {
          const newMessages: Message[] = convertLocalMessagesToMessages(event.data.localMessages);
          setMessages(newMessages);
        }

        // 更新流式状态
        updateStreamingStatus(event.data.localMessages || []);
      }
    });

    // 监听部分消息事件，参考 VSCode 插件的 COMPOSER_PARTIAL_MESSAGE
    newSocket.on('composerPartialMessage', (event) => {
      console.log('收到部分消息:', event);
      if (event.sessionId === currentSessionIdRef.current && event.data.partialMessage) {
        const partialMsg = event.data.partialMessage;

        // 更新 composer 状态中的 localMessages
        setComposerState(prevState => {
          const lastIndex = prevState.localMessages.findLastIndex(msg => msg.ts === partialMsg.ts);
          if (lastIndex !== -1) {
            const newLocalMessages = [...prevState.localMessages];
            newLocalMessages[lastIndex] = partialMsg;
            console.log(111,prevState.localMessages)
              console.log(22,newLocalMessages)
            return { ...prevState, localMessages: newLocalMessages };
          }
          return prevState;
        });

        // 更新前端消息显示
        setMessages(prev => {
          const existingIndex = prev.findIndex(msg => msg.id === partialMsg.ts?.toString());
          const newMessage: Message = convertLocalMessageToMessage(partialMsg);

          if (existingIndex >= 0) {
            // 更新现有消息
            const updated = [...prev];
            updated[existingIndex] = newMessage;
            return updated;
          } else {
            // 添加新消息
            return [...prev, newMessage];
          }
        });
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  // 同步 currentSessionId 到 ref
  useEffect(() => {
    currentSessionIdRef.current = currentSessionId;
  }, [currentSessionId]);

  // 当会话切换时，订阅新会话的消息
  useEffect(() => {
    if (socket && currentSessionId) {
      console.log('订阅会话:', currentSessionId);
      socket.emit('subscribeSession', { sessionId: currentSessionId });

      return () => {
        console.log('取消订阅会话:', currentSessionId);
        socket.emit('unsubscribeSession', { sessionId: currentSessionId });
      };
    }
  }, [socket, currentSessionId]);

  // 首次加载
  useEffect(() => {
    fetchSessions();
  }, []);

  // 处理URL参数变化
  useEffect(() => {
    if (sessionId && sessionId !== currentSessionId) {
      // 如果URL中有sessionId且与当前不同，则切换到该会话
      setCurrentSessionId(sessionId);
      currentSessionIdRef.current = sessionId;

      // 加载会话数据，使用与switchSession相同的逻辑
      const loadSession = async () => {
        try {
          const response = await fetch(`${API_URL}/sessions/${sessionId}`);
          if (response.ok) {
            const data = await response.json();

            // 优先使用 localMessages 更新 composerState
            if (data.localMessages && data.localMessages.length > 0) {
              // 清理历史消息的 partial 状态，避免显示多余的光标
              const cleanedLocalMessages = data.localMessages.map((msg: LocalMessage) => ({
                ...msg,
                partial: false, // 历史消息都不应该是 partial 状态
              }));

              setComposerState(prevState => ({
                ...prevState,
                localMessages: cleanedLocalMessages,
                sessionId: sessionId,
                workspaceUri: data.workspaceUri || prevState.workspaceUri,
                currentMessageTs: data.currentMessageTs,
                editingMessageTs: data.editingMessageTs,
                indexed: data.indexed,
              }));

              // 清空传统的 messages，让 ConversationRenderer 来渲染
              setMessages([]);
              // 清理流式状态
              setIsStreaming(false);
            } else {
              // 如果没有 localMessages，使用传统的 messages 作为 fallback
              setMessages(data.messages || []);
              // 清空 localMessages
              setComposerState(prevState => ({
                ...prevState,
                localMessages: [],
                sessionId: sessionId,
                workspaceUri: data.workspaceUri || prevState.workspaceUri,
                currentMessageTs: data.currentMessageTs,
                editingMessageTs: data.editingMessageTs,
                indexed: data.indexed,
              }));
              // 清理流式状态
              setIsStreaming(false);
            }
          } else {
            // 如果会话不存在，跳转到首页
            navigate('/');
          }
        } catch (error) {
          console.error('加载会话失败:', error);
          navigate('/');
        }
      };

      loadSession();
    } else if (!sessionId && currentSessionId) {
      // 如果URL中没有sessionId但当前有会话，清空当前会话
      setCurrentSessionId('');
      currentSessionIdRef.current = '';
      setComposerState(prevState => ({
        ...prevState,
        localMessages: [],
        sessionId: '',
      }));
      setMessages([]);
    }
  }, [sessionId, navigate]);

  // 调试：监听 currentSessionId 变化
  useEffect(() => {
    console.log('Current session ID changed:', currentSessionId);
  }, [currentSessionId]);

  // 消息变化时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 输入变化时调整高度
  useEffect(() => {
    adjustTextareaHeight();
  }, [question]);

  return (
    <div className="app-container">
      <div className="sidebar">
        <div className="sidebar-header">
          <h2>DeepSearch</h2>
          <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
            <span className="status-dot"></span>
            {isConnected ? '已连接' : '未连接'}
          </div>
        </div>
        <button className="new-chat-btn" onClick={createNewSession}>
          + 新对话
        </button>
        <div className="session-list">
          {sessions.map((session) => (
            <div 
              key={session.id}
              className={`session-item ${currentSessionId === session.id ? 'active' : ''}`}
              onClick={() => switchSession(session.id)}
            >
              <span className="session-name">{truncateText(session.name, 30)}</span>
              <span className="session-count">{session.messageCount}条消息</span>
            </div>
          ))}
        </div>
      </div>

      <div className="main-content">
        <div className="chat-container">
          <div className="messages-container">
            {/* 使用新的对话渲染器，优先显示 localMessages */}
            <ConversationRenderer
              localMessages={composerState.localMessages}
              isStreaming={isStreaming}
            />

            {/* 如果没有 localMessages，则显示传统的 messages */}
            {composerState.localMessages.length === 0 && messages.length > 0 && (
              <div className="fallback-messages">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`message ${message.role === 'user' ? 'user-message' : 'assistant-message'}`}
                  >
                    <div className="message-content">{message.content}</div>
                  </div>
                ))}
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="input-container">
            <textarea
              ref={textareaRef}
              rows={1}
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入问题，按Enter发送..."
              disabled={loading}
              className="question-input"
            />
            <button 
              onClick={handleSendMessage} 
              disabled={loading || !question.trim()} 
              className="send-button"
            >
              {loading ? '发送中...' : '发送'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// 主App组件 - 处理路由
export default function App() {
  return (
    <Routes>
      <Route path="/" element={<SessionView />} />
      <Route path="/:sessionId" element={<SessionView />} />
    </Routes>
  );
}
