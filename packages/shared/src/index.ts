/**
 * DeepSearch 共享类型和工具函数
 */

// 参考 VSCode 插件的 LocalMessage 类型定义
export interface LocalMessage {
  /** 消息的时间戳(timestamp) */
  ts: number;
  role?: "user";
  sessionId: string;
  chatId?: string;

  /** 消息类型：请求类消息(ask)或者回复类消息(say) */
  type: "ask" | "say";

  /** 当type为"ask"时的具体请求类型 */
  ask?: string;

  /** 当type为"say"时的具体响应类型 */
  say?: string;

  /** 消息的具体文本内容 */
  text?: string;

  /** 是否为部分消息(未完成的消息) */
  partial?: boolean;

  /** 最后检查点的哈希值 */
  lastCheckpointHash?: string;

  /** 检查点是否已签出 */
  isCheckpointCheckedOut?: boolean;

  /** 对话历史索引 */
  conversationHistoryIndex?: number;

  /** 对话历史被截断的范围 */
  conversationHistoryDeletedRange?: [number, number];
}

// 兼容性：保留原有的简化 Message 接口用于前端显示
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  createdAt: number;
}

// 会话缓存路径信息类型 - 参考 VSCode 插件
export interface SessionCachePathInfo {
  filepath?: string;
  startLine?: number;
  endLine?: number;
  order: number;
  cacheKey?: string;
}

// 参考 VSCode 插件的 PersistedComposerSessionData 结构
export interface Session {
  /** 会话ID */
  sessionId: string;
  /** 会话名称 */
  name?: string;
  /** 工作空间URI */
  workspaceUri: string;
  /** 当前消息时间戳 */
  currentMessageTs?: number;
  /** 正在编辑的消息时间戳 */
  editingMessageTs?: number;
  /** 本地消息列表 */
  localMessages: LocalMessage[];
  /** 是否已索引 */
  indexed?: boolean;
  /** 缓存路径信息 */
  cachePathInfos?: SessionCachePathInfo[];
  /** 缓存路径信息的最后刷新时间戳 */
  cachePathInfosLastRefresh?: number;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
}

// 会话配置 - 保留用于向后兼容
export interface SessionConfig {
  model?: string;
  temperature?: number;
  contextItems?: string[];
  systemPrompt?: string;
}

// 会话摘要 (用于列表显示)
export interface SessionSummary {
  id: string;
  name: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
}

// API 请求类型
export namespace ApiRequests {
  // 问题请求接口
  export interface Question {
    question: string;
  }
  
  // 会话创建请求
  export interface CreateSession {
    name?: string;
    model?: string;
    systemPrompt?: string;
  }
  
  // 消息发送请求
  export interface SendMessage {
    content: string;
  }
}

// API 响应类型
export namespace ApiResponses {
  // 问题回答响应
  export interface Answer {
    answer: string;
  }
  
  // 创建会话响应
  export interface SessionCreated {
    id: string;
    name: string;
    createdAt: number;
  }
  
  // 消息回复响应
  export interface MessageResponse {
    id: string;
    content: string;
    role: string;
    createdAt: number;
  }
}

// 工具函数
export const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// 搜索相关类型
export interface SearchRequest {
  query: string;
  limit?: number;
}

export interface SearchResult {
  file: string;
  content: string;
  line: number;
  score: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
}

// 索引状态类型
export interface IndexState {
  indexed: boolean;
  indexing: boolean;
  indexingProgress: number;
  indexingMessage: string;
  lastBuildTime: string;
  pauseIndexManual: boolean;
  status: 'indexing' | 'indexed' | 'paused';
}

// 代理配置类型
export interface AgentConfig {
  repoPath?: string;
  enableRepoIndex?: boolean;
  maxIndexSpace?: number;
  proxyUrl?: string;
  agentPreference?: string;
  userName?: string;
}

// 项目信息类型
export interface ProjectInfo {
  name: string;
  gitRepo: string;
  gitRemote: string;
  currentBranchName: string;
  username: string;
  userEmail: string;
  repoPath: string;
  currentCommit: string;
}

// 服务状态类型
export interface ServiceStatus {
  status: 'ok' | 'error';
  message?: string;
  timestamp: string;
}

// 扩展 API 请求类型
export namespace ApiRequests {
  // 问题请求接口
  export interface Question {
    question: string;
  }

  // 会话创建请求
  export interface CreateSession {
    name?: string;
    model?: string;
    systemPrompt?: string;
  }

  // 消息发送请求
  export interface SendMessage {
    content: string;
  }

  // 搜索请求
  export interface Search {
    query: string;
    limit?: number;
  }
}

// 扩展 API 响应类型
export namespace ApiResponses {
  // 问题回答响应
  export interface Answer {
    answer: string;
  }

  // 创建会话响应
  export interface SessionCreated {
    id: string;
    name: string;
    createdAt: number;
  }

  // 消息回复响应
  export interface MessageResponse {
    id: string;
    content: string;
    role: string;
    createdAt: number;
  }

  // 搜索响应
  export interface Search {
    results: SearchResult[];
    total: number;
    query: string;
  }

  // 索引状态响应
  export interface IndexStatus extends IndexState {}

  // 配置响应
  export interface Config extends AgentConfig {}

  // 服务状态响应
  export interface Status extends ServiceStatus {}
}
