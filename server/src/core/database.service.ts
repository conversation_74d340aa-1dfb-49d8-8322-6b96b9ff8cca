import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import * as sqlite3 from 'sqlite3';
import * as path from 'path';
import * as fs from 'fs';
import { Session, Message, LocalMessage } from '@deepsearch/shared';

/**
 * 数据库服务 - 使用 SQLite 进行会话持久化
 */
@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DatabaseService.name);
  private db: sqlite3.Database | null = null;
  private readonly dbPath: string;

  constructor() {
    // 数据库文件路径
    const storageDir = path.join(process.cwd(), '.deepsearch');
    this.dbPath = path.join(storageDir, 'sessions.db');
    
    // 确保存储目录存在
    if (!fs.existsSync(storageDir)) {
      fs.mkdirSync(storageDir, { recursive: true });
    }
  }

  async onModuleInit() {
    await this.initDatabase();
  }

  async onModuleDestroy() {
    if (this.db) {
      await this.closeDatabase();
    }
  }

  /**
   * 初始化数据库连接和表结构
   */
  private async initDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          this.logger.error('数据库连接失败:', err);
          reject(err);
          return;
        }

        this.logger.log(`数据库连接成功: ${this.dbPath}`);
        
        // 创建表结构
        this.createTables()
          .then(() => resolve())
          .catch(reject);
      });
    });
  }

  /**
   * 创建数据库表 - 基于 VSCode 插件的 PersistedComposerSessionData 结构
   */
  private async createTables(): Promise<void> {
    const createSessionsTable = `
      CREATE TABLE IF NOT EXISTS sessions (
        session_id TEXT PRIMARY KEY,
        name TEXT,
        workspace_uri TEXT NOT NULL,
        current_message_ts INTEGER,
        editing_message_ts INTEGER,
        local_messages TEXT NOT NULL DEFAULT '[]',
        indexed INTEGER DEFAULT 0,
        cache_path_infos TEXT DEFAULT '[]',
        cache_path_infos_last_refresh INTEGER,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    `;

    // 保留 messages 表用于向后兼容，但标记为已弃用
    const createMessagesTable = `
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        role TEXT NOT NULL,
        content TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (session_id) REFERENCES sessions (session_id) ON DELETE CASCADE
      )
    `;

    const createIndexes = [
      'CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages (session_id)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_updated_at ON sessions (updated_at)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_session_id ON sessions (session_id)',
    ];

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      this.db.serialize(() => {
        this.db!.run(createSessionsTable, (err) => {
          if (err) {
            this.logger.error('创建 sessions 表失败:', err);
            reject(err);
            return;
          }
        });

        this.db!.run(createMessagesTable, (err) => {
          if (err) {
            this.logger.error('创建 messages 表失败:', err);
            reject(err);
            return;
          }
        });

        // 创建索引
        createIndexes.forEach(indexSql => {
          this.db!.run(indexSql, (err) => {
            if (err) {
              this.logger.error('创建索引失败:', err);
            }
          });
        });

        this.logger.log('数据库表创建完成');
        resolve();
      });
    });
  }

  /**
   * 关闭数据库连接
   */
  private async closeDatabase(): Promise<void> {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            this.logger.error('关闭数据库失败:', err);
          } else {
            this.logger.log('数据库连接已关闭');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 保存会话 - 基于新的 Session 结构
   */
  async saveSession(session: Session): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        INSERT OR REPLACE INTO sessions (
          session_id, name, workspace_uri, current_message_ts, editing_message_ts,
          local_messages, indexed, cache_path_infos, cache_path_infos_last_refresh,
          created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      this.db.run(
        sql,
        [
          session.sessionId,
          session.name || null,
          session.workspaceUri,
          session.currentMessageTs || null,
          session.editingMessageTs || null,
          JSON.stringify(session.localMessages),
          session.indexed ? 1 : 0,
          JSON.stringify(session.cachePathInfos || []),
          session.cachePathInfosLastRefresh || null,
          session.createdAt,
          session.updatedAt,
        ],
        function (err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  /**
   * 保存消息
   */
  async saveMessage(sessionId: string, message: Message): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        INSERT OR REPLACE INTO messages (id, session_id, role, content, created_at)
        VALUES (?, ?, ?, ?, ?)
      `;

      this.db.run(
        sql,
        [message.id, sessionId, message.role, message.content, message.createdAt],
        function (err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  /**
   * 获取所有会话 - 基于新的 Session 结构
   */
  async getAllSessions(): Promise<Session[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        SELECT * FROM sessions
        ORDER BY updated_at DESC
      `;

      this.db.all(sql, [], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const sessions: Session[] = rows.map(row => ({
            sessionId: row.session_id,
            name: row.name,
            workspaceUri: row.workspace_uri,
            currentMessageTs: row.current_message_ts,
            editingMessageTs: row.editing_message_ts,
            localMessages: JSON.parse(row.local_messages || '[]'),
            indexed: Boolean(row.indexed),
            cachePathInfos: JSON.parse(row.cache_path_infos || '[]'),
            cachePathInfosLastRefresh: row.cache_path_infos_last_refresh,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
          }));

          resolve(sessions);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * 获取单个会话 - 基于新的 Session 结构
   */
  async getSession(id: string): Promise<Session | undefined> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = 'SELECT * FROM sessions WHERE session_id = ?';

      this.db.get(sql, [id], (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(undefined);
          return;
        }

        try {
          resolve({
            sessionId: row.session_id,
            name: row.name,
            workspaceUri: row.workspace_uri,
            currentMessageTs: row.current_message_ts,
            editingMessageTs: row.editing_message_ts,
            localMessages: JSON.parse(row.local_messages || '[]'),
            indexed: Boolean(row.indexed),
            cachePathInfos: JSON.parse(row.cache_path_infos || '[]'),
            cachePathInfosLastRefresh: row.cache_path_infos_last_refresh,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
          });
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * 获取会话的所有消息
   */
  async getSessionMessages(sessionId: string): Promise<Message[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        SELECT * FROM messages 
        WHERE session_id = ? 
        ORDER BY created_at ASC
      `;

      this.db.all(sql, [sessionId], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const messages: Message[] = rows.map(row => ({
          id: row.id,
          role: row.role as 'user' | 'assistant' | 'system',
          content: row.content,
          createdAt: row.created_at,
        }));

        resolve(messages);
      });
    });
  }

  /**
   * 删除会话 - 基于新的 Session 结构
   */
  async deleteSession(id: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      // 先删除消息（向后兼容），再删除会话
      this.db.serialize(() => {
        this.db!.run('DELETE FROM messages WHERE session_id = ?', [id], (err) => {
          if (err) {
            reject(err);
            return;
          }
        });

        this.db!.run('DELETE FROM sessions WHERE session_id = ?', [id], function (err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        });
      });
    });
  }

  /**
   * 更新会话的更新时间 - 基于新的 Session 结构
   */
  async updateSessionTimestamp(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = 'UPDATE sessions SET updated_at = ? WHERE session_id = ?';

      this.db.run(sql, [Date.now(), id], function (err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
}
