import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { LocalService } from "./local-service";
import { ProjectService } from "./project.service";
import { ConfigService } from "../config/config.service";
import { EventBusService } from "./event-bus";
import { DatabaseService } from "./database.service";
import { DEEPSEARCH_SIMPLE_PROMPT } from "../config/system-prompts";
import * as uuid from "uuid";
import * as os from "os";
import {
  Message,
  Session,
  SessionConfig,
  LocalMessage,
} from "@deepsearch/shared";
import { WebviewMessage } from "src/types/agent";

// 定义消息推送事件类型
export interface ComposerStateUpdateEvent {
  sessionId: string;
  localMessages: any[];
  workspaceUri: string;
  currentTaskInterrupted: boolean;
  indeterminatedWorkingSetEffects: any[];
  isCurrentWorkspaceSession: boolean;
  editingMessageTs?: number;
  currentMessageTs?: number;
  userPreferredModel?: string;
  localServiceConnectionLost: boolean;
  indexed?: any;
  cachePathInfos?: any[];
}

export interface ComposerPartialMessageEvent {
  sessionId: string;
  partialMessage: any;
}

/**
 * 对话服务 - 负责处理用户与AI的对话
 */
@Injectable()
export class ComposerService implements OnModuleInit {
  private readonly logger = new Logger(ComposerService.name);
  private sessions: Map<string, Session> = new Map();
  private readonly defaultSystemPrompt =
    "你是一个有用的AI助手，专注于回答用户的问题，并提供准确的信息。" +
    "如果你不知道某个问题的答案，请直接说不知道，不要编造信息。";

  constructor(
    private readonly localService: LocalService,
    private readonly configService: ConfigService,
    private readonly projectService: ProjectService,
    private readonly eventBus: EventBusService,
    private readonly databaseService: DatabaseService
  ) {
    // 不在构造函数中设置事件处理器，等待模块初始化完成
  }

  /**
   * 模块初始化完成后设置事件处理器
   */
  async onModuleInit() {
    // 加载持久化的会话数据
    await this.loadSessionsFromDatabase();

    // 等待一小段时间确保 LocalService 完全初始化
    setTimeout(() => {
      this.setupEventHandlers();
    }, 100);
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    // 处理来自二进制服务的部分消息更新
    this.localService.onMessage("assistant/agent/message", (message) => {
      const data = message.data;
      if (data && data.sessionId && data.text) {
        this.logger.debug(
          `收到部分消息: ${data.sessionId} - ${data.text.slice(0, 50)}...`
        );

        // 推送部分消息给前端，参考 VSCode 插件的实现
        this.postComposerPartialMessage(data.sessionId, {
          sessionId: data.sessionId,
          text: data.text,
          type: data.type,
          ts: data.ts,
          partial: true,
          role: undefined,
        });
      }
      return { status: "ok" };
    });

    // 处理完整消息列表更新，参考 VSCode 插件的实现
    this.localService.onMessage(
      "assistant/agent/messageList",
      async (message) => {
        const messages = message.data;
        if (messages && messages.length > 0) {
          const sessionId = messages[0].sessionId;
          this.logger.debug(
            `收到消息列表更新: ${sessionId}, ${messages.length}条消息`
          );

          // 获取或创建会话
          let session = await this.getSession(sessionId);
          if (!session) {
            // 如果会话不存在，创建一个新会话
            session = await this.createSession();
            // 更新会话ID为实际的sessionId
            session.sessionId = sessionId;
            await this.databaseService.saveSession(session);
          }

          // 直接使用来自二进制服务的 LocalMessage 数据，这些数据已经包含正确的 ask/say 字段
          // 确保消息格式正确
          const validatedMessages: LocalMessage[] = messages.map((msg) => ({
            ts: msg.ts || Date.now(),
            role: msg.role,
            sessionId: msg.sessionId || sessionId,
            chatId: msg.chatId,
            type: msg.type || "say",
            ask: msg.ask,
            say: msg.say,
            text: msg.text || "",
            partial: msg.partial || false,
            lastCheckpointHash: msg.lastCheckpointHash,
            isCheckpointCheckedOut: msg.isCheckpointCheckedOut,
            conversationHistoryIndex: msg.conversationHistoryIndex,
            conversationHistoryDeletedRange:
              msg.conversationHistoryDeletedRange,
          }));

          // 使用智能合并逻辑，参考 VSCode 插件的实现
          const mergedLocalMessages = this.mergeLocalMessages(
            session.localMessages || [],
            validatedMessages
          );

          // 更新内存中的会话
          session.localMessages = mergedLocalMessages;
          session.updatedAt = Date.now();
          if (mergedLocalMessages.length > 0) {
            session.currentMessageTs =
              mergedLocalMessages[mergedLocalMessages.length - 1].ts;
          }
          this.sessions.set(sessionId, session);

          // 保存到数据库
          try {
            // 更新会话基本信息（包含 localMessages）
            await this.databaseService.saveSession(session);

            this.logger.debug(
              `会话 ${sessionId} 已保存到数据库，包含 ${mergedLocalMessages.length} 条本地消息`
            );
          } catch (error) {
            this.logger.error(`保存会话到数据库失败: ${error}`);
          }

          // 推送完整消息列表给前端，使用正确处理过的 LocalMessage 数据
          this.postComposerStateUpdate(sessionId, {
            sessionId,
            localMessages: mergedLocalMessages, // 使用合并后的正确数据
            workspaceUri: session.workspaceUri || process.cwd(),
            currentTaskInterrupted: false,
            indeterminatedWorkingSetEffects: [],
            isCurrentWorkspaceSession: true,
            localServiceConnectionLost: false,
            cachePathInfos: session.cachePathInfos || [],
            currentMessageTs: session.currentMessageTs,
            editingMessageTs: session.editingMessageTs,
            indexed: session.indexed,
          });
        }
        return { status: "ok", data: messages };
      }
    );

    // 处理文件编辑请求
    this.localService.onMessage("assistant/agent/editFile", (message) => {
      const { path, content, language, instructions } = message.data || {};
      this.logger.debug(`收到文件编辑请求: ${path}`);

      // 使用同步返回方式，在后台处理异步操作
      this.handleFileEdit(path, content, language, instructions)
        .then(() => this.logger.debug(`文件编辑处理完成: ${path}`))
        .catch((error) =>
          this.logger.error(`文件编辑处理失败: ${path}`, error)
        );

      // 立即返回成功响应
      return {
        status: "ok",
        data: { type: "success", path },
      };
    });

    // 处理命令执行请求
    this.localService.onMessage("assistant/agent/executeCommand", (message) => {
      const { command, is_background } = message.data || {};
      this.logger.debug(`收到命令执行请求: ${command}`);

      // 使用同步返回方式，在后台处理异步操作
      this.handleCommandExecution(command, is_background)
        .then((result) => this.logger.debug(`命令执行完成: ${command}`, result))
        .catch((error) => this.logger.error(`命令执行失败: ${command}`, error));

      // 立即返回成功响应
      return {
        status: "ok",
        data: {
          userRejected: false,
          result: `正在执行命令: ${command}`,
          completed: false,
        },
      };
    });

    // 处理环境信息请求
    this.localService.onMessage("assistant/agent/environment", (message) => {
      const { includeFileDetails } = message.data || {};
      this.logger.debug(
        `收到环境信息请求: includeFileDetails=${includeFileDetails}`
      );

      // 使用同步返回方式，在后台收集环境信息
      this.collectEnvironmentInfo(includeFileDetails)
        .then(() => this.logger.debug("环境信息收集完成"))
        .catch((error) => this.logger.error("环境信息收集失败", error));

      // 立即返回基本环境信息
      return {
        status: "ok",
        data: `<environment_details>\n# 当前工作目录\n${process.cwd()}\n\n# 当前时间\n${new Date().toLocaleString()}\n</environment_details>`,
      };
    });
  }

  // 处理文件编辑的异步方法
  private async handleFileEdit(
    path: string,
    content: string,
    language: string,
    instructions: string
  ): Promise<void> {
    try {
      // 在这里实现实际的文件编辑逻辑
      this.logger.log(
        `处理文件编辑: ${path}, 语言: ${language}, 指令: ${instructions}`
      );
      this.logger.debug(`文件内容长度: ${content?.length || 0}`);
      // 例如: await fs.writeFile(path, content);
    } catch (error) {
      this.logger.error(`文件编辑失败: ${path}`, error);
      throw error;
    }
  }

  // 处理命令执行的异步方法
  private async handleCommandExecution(
    command: string,
    is_background: boolean
  ): Promise<any> {
    try {
      // 在这里实现实际的命令执行逻辑
      this.logger.log(`执行命令: ${command}, 后台运行: ${is_background}`);
      // 例如: const result = await exec(command);
      return { completed: true, result: `模拟执行命令结果: ${command}` };
    } catch (error) {
      this.logger.error(`命令执行失败: ${command}`, error);
      throw error;
    }
  }

  // 收集环境信息的异步方法
  private async collectEnvironmentInfo(
    includeFileDetails: boolean
  ): Promise<string> {
    try {
      // 在这里实现实际的环境信息收集逻辑
      this.logger.log(`收集环境信息: includeFileDetails=${includeFileDetails}`);
      // 例如: const files = includeFileDetails ? await listFiles() : [];
      return `<environment_details>\n# 详细环境信息\n${process.cwd()}\n\n# 当前时间\n${new Date().toLocaleString()}\n</environment_details>`;
    } catch (error) {
      this.logger.error("收集环境信息失败", error);
      throw error;
    }
  }

  /**
   * 创建新会话 - 参考 VSCode 插件的 PersistedComposerSessionData 结构
   */
  async createSession(config: Partial<SessionConfig> = {}): Promise<Session> {
    const sessionId = uuid.v4();
    const now = Date.now();
    const projectInfo = this.projectService.getProjectInfo();

    const session: Session = {
      sessionId,
      name: config.systemPrompt
        ? `对话 ${new Date().toLocaleDateString()}`
        : undefined,
      workspaceUri: projectInfo?.repoPath || process.cwd(),
      currentMessageTs: undefined,
      editingMessageTs: undefined,
      localMessages: [],
      indexed: false,
      cachePathInfos: [],
      cachePathInfosLastRefresh: undefined,
      createdAt: now,
      updatedAt: now,
    };

    // 添加系统消息作为 LocalMessage
    if (config.systemPrompt || this.defaultSystemPrompt) {
      const systemLocalMessage: LocalMessage = {
        ts: now,
        role: undefined, // 系统消息没有 role
        sessionId,
        type: "say",
        say: "text",
        text: config.systemPrompt || this.defaultSystemPrompt,
        partial: false,
      };
      session.localMessages.push(systemLocalMessage);
    }

    // 保存会话到数据库
    await this.databaseService.saveSession(session);

    // 同时保存到内存缓存
    this.sessions.set(sessionId, session);

    return session;
  }

  /**
   * 获取会话
   */
  async getSession(id: string): Promise<Session | undefined> {
    // 先从内存缓存中查找
    let session = this.sessions.get(id);
    if (session) {
      return session;
    }

    // 如果内存中没有，从数据库加载
    session = await this.databaseService.getSession(id);
    if (session) {
      // 加载到内存缓存
      this.sessions.set(id, session);
    }

    return session;
  }

  /**
   * 获取所有会话
   */
  async getAllSessions(): Promise<Session[]> {
    // 从数据库获取最新的会话列表
    const sessions = await this.databaseService.getAllSessions();

    // 更新内存缓存
    this.sessions.clear();
    for (const session of sessions) {
      this.sessions.set(session.sessionId, session);
    }

    return sessions;
  }

  /**
   * 删除会话
   */
  async deleteSession(id: string): Promise<boolean> {
    // 从数据库删除
    const result = await this.databaseService.deleteSession(id);

    // 从内存缓存删除
    this.sessions.delete(id);

    return result;
  }

  /**
   * 发送消息并获取回答 - 参考 VSCode 插件的 LocalMessage 结构
   */
  async sendMessage(sessionId: string, content: string): Promise<Message> {
    // 获取或创建会话
    let session = await this.getSession(sessionId);
    if (!session) {
      session = await this.createSession();
      sessionId = session.sessionId;
    }

    const now = Date.now();

    // 添加用户消息作为 LocalMessage
    const userLocalMessage: LocalMessage = {
      ts: now,
      role: "user",
      sessionId,
      type: "say",
      say: "text",
      text: content,
      partial: false,
    };

    session.localMessages.push(userLocalMessage);
    session.currentMessageTs = now;
    session.updatedAt = now;
    try {
      // 发送请求到本地二进制服务
      this.logger.debug(
        `发送消息到AI服务: ${sessionId}, ${content.slice(0, 50)}...`
      );

      try {
        // 发送请求给本地二进制服务，使用与 VSCode 插件完全相同的参数格式
        // 参考 useSubmit.ts 中 $postMessageToComposerEngine 的调用
        const chatId = uuid.v4();
        // 获取项目信息
        const projectInfo = this.projectService.getProjectInfo();

        const payload: WebviewMessage<"newTask"> = {
          type: "newTask",
          task: content,
          taskForLlm: content, // todo
          reqData: {
            sessionId,
            chatId,
            username: this.configService.getUserName(),
            messages: [],
            systemPrompt: DEEPSEARCH_SIMPLE_PROMPT, // 使用完整的系统提示词
            deviceInfo: {
              deviceId: uuid.v4(), // 生成唯一设备ID
              deviceModel: os.hostname(),
              deviceName: os.hostname(),
              deviceOsName: os.type(),
              deviceOsVersion: os.release(),
              ide: "vscode",
              ideVersion: "1.0.0",
              platform: "kwaipilot-vscode",
              pluginVersion: "1.0.0",
            },
            projectInfo: {
              gitUrl: projectInfo?.gitRemote || "",
              openedFilePath: "", // Web环境下没有当前打开的文件
              projectName: projectInfo?.name || "",
            },
          },
          localMessages: [], // Add this line to satisfy the required property
          contextItems: [], // TODO: 转换 session.config.contextItems 为正确的类型
          images: [],
          // Remove rules, editorState, questionForHumanReading, editingMessageTs, model if not required by the type
        };
        const response = await this.localService.request(
          "assistant/agent/local",
          payload
        );

        this.logger.debug(
          `收到AI服务响应: ${JSON.stringify(response).slice(0, 200)}...`
        );

        // 从响应中提取AI回复内容
        let assistantContent = "";
        if (response && response.status === "ok") {
          // 根据实际的响应格式解析内容
          if (
            typeof response.data === "string" &&
            (response.data as string).trim()
          ) {
            // 如果data是非空字符串，直接使用
            assistantContent = response.data as string;
          } else if (response.data && typeof response.data === "object") {
            const data = response.data as any;
            if (data.content) {
              assistantContent = data.content;
            } else if (data.message) {
              assistantContent = data.message;
            } else {
              // 如果响应格式不符合预期，使用默认回复
              assistantContent = "抱歉，我无法理解您的问题，请重新表述。";
              this.logger.warn(
                `未知的AI服务响应格式: ${JSON.stringify(response.data)}`
              );
            }
          } else if (response.message) {
            // 如果data为空但有message，使用message作为回复
            assistantContent = response.message;
          } else {
            // 如果响应格式不符合预期，使用默认回复
            assistantContent = "抱歉，我无法理解您的问题，请重新表述。";
            this.logger.warn(
              `未知的AI服务响应格式: response=${JSON.stringify(response)}`
            );
          }
        } else {
          assistantContent = "抱歉，AI服务暂时不可用，请稍后重试。";
          this.logger.error(`AI服务返回错误: ${JSON.stringify(response)}`);
        }

        // 添加助手回复作为 LocalMessage
        const assistantTs = Date.now();
        const assistantLocalMessage: LocalMessage = {
          ts: assistantTs,
          role: undefined, // 助手消息没有 role
          sessionId,
          type: "say",
          say: "text",
          text: assistantContent,
          partial: false,
        };

        session.localMessages.push(assistantLocalMessage);
        session.currentMessageTs = assistantTs;
        session.updatedAt = Date.now();

        // 返回兼容的 Message 格式用于 API 响应
        const assistantMessage: Message = {
          role: "assistant",
          content: assistantContent,
          id: assistantTs.toString(),
          createdAt: assistantTs,
        };

        return assistantMessage;
      } catch (localServiceError) {
        // 如果本地服务不可用，返回错误信息
        this.logger.error(`本地AI服务调用失败: ${localServiceError}`);

        const errorContent =
          "抱歉，AI服务暂时不可用。可能的原因：\n1. 本地代理服务未启动\n2. 二进制服务连接失败\n3. 网络连接问题\n\n请检查服务状态后重试。";
        const errorTs = Date.now();

        // 添加错误消息作为 LocalMessage
        const errorLocalMessage: LocalMessage = {
          ts: errorTs,
          role: undefined,
          sessionId,
          type: "say",
          say: "text",
          text: errorContent,
          partial: false,
        };

        session.localMessages.push(errorLocalMessage);
        session.currentMessageTs = errorTs;
        session.updatedAt = Date.now();

        // 返回兼容的 Message 格式
        const errorMessage: Message = {
          role: "assistant",
          content: errorContent,
          id: errorTs.toString(),
          createdAt: errorTs,
        };

        return errorMessage;
      }
    } catch (error) {
      this.logger.error(`发送消息失败: ${sessionId}`, error);

      const errorContent = `发送消息时出现错误: ${error}`;
      const errorTs = Date.now();

      // 添加错误消息作为 LocalMessage
      const errorLocalMessage: LocalMessage = {
        ts: errorTs,
        role: undefined,
        sessionId,
        type: "say",
        say: "text",
        text: errorContent,
        partial: false,
      };

      session.localMessages.push(errorLocalMessage);
      session.currentMessageTs = errorTs;
      session.updatedAt = Date.now();

      // 返回兼容的 Message 格式
      const errorMessage: Message = {
        role: "assistant",
        content: errorContent,
        id: errorTs.toString(),
        createdAt: errorTs,
      };

      return errorMessage;
    }
  }

  /**
   * 简单问答接口
   */
  async ask(question: string): Promise<string> {
    try {
      // 创建临时会话进行问答
      const session = await this.createSession();

      this.logger.debug(`简单问答: ${question.slice(0, 50)}...`);

      const reply = await this.sendMessage(session.sessionId, question);

      // 使用完后删除临时会话，避免占用内存
      this.deleteSession(session.sessionId);

      return reply.content;
    } catch (error) {
      this.logger.error(`问答失败: ${question}`, error);
      return `发生错误: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }

  /**
   * 流式问答接口
   */
  async *askStream(question: string): AsyncGenerator<string> {
    try {
      // 创建临时会话进行流式问答
      const session = await this.createSession();

      // 添加用户消息作为 LocalMessage
      const now = Date.now();
      const userLocalMessage: LocalMessage = {
        ts: now,
        role: "user",
        sessionId: session.sessionId,
        type: "say",
        say: "text",
        text: question,
        partial: false,
      };
      session.localMessages.push(userLocalMessage);
      session.currentMessageTs = now;

      this.logger.debug(`开始流式问答: ${question.slice(0, 50)}...`);

      try {
        // 发送流式请求到本地二进制服务，使用与 VSCode 插件相同的参数格式
        const chatId = uuid.v4();
        const projectInfo = this.projectService.getProjectInfo();

        const payload: WebviewMessage<"newTask"> = {
          type: "newTask",
          task: question,
          taskForLlm: question,
          reqData: {
            sessionId: session.sessionId,
            chatId,
            username: this.configService.getUserName(),
            messages: [],
            systemPrompt: DEEPSEARCH_SIMPLE_PROMPT, // 使用完整的系统提示词
            deviceInfo: {
              deviceId: uuid.v4(),
              deviceModel: os.hostname(),
              deviceName: os.hostname(),
              deviceOsName: os.type(),
              deviceOsVersion: os.release(),
              ide: "deepsearch-web",
              ideVersion: "1.0.0",
              platform: "deepsearch-server",
              pluginVersion: "1.0.0",
            },
            projectInfo: {
              gitUrl: projectInfo?.gitRemote || "",
              openedFilePath: "",
              projectName: projectInfo?.name || "",
            },
          },
          localMessages: [],
          contextItems: [], // TODO: 转换 session.config.contextItems 为正确的类型
          images: [],
        };

        const response = await this.localService.request(
          "assistant/agent/local",
          payload
        );

        if (response.status === "ok") {
          // 如果支持流式响应，这里应该监听流式数据
          // 目前先模拟流式输出
          const data = response.data as any;
          const fullResponse = data?.content || data || "抱歉，无法获取回答。";
          const words = fullResponse.split(" ");

          for (let i = 0; i < words.length; i++) {
            yield words.slice(0, i + 1).join(" ");
            await new Promise((resolve) => setTimeout(resolve, 100)); // 模拟流式延迟
          }
        } else {
          yield "抱歉，AI服务暂时不可用。";
        }
      } catch (localServiceError) {
        this.logger.error(`流式AI服务调用失败: ${localServiceError}`);
        yield "抱歉，AI服务连接失败，请检查服务状态。";
      }

      // 清理临时会话
      this.deleteSession(session.sessionId);
    } catch (error) {
      this.logger.error(`流式问答失败: ${question}`, error);
      yield `发生错误: ${error}`;
    }
  }

  /**
   * 更新会话配置 - 已弃用，新的 Session 结构不再使用 config
   * @deprecated 新的 Session 结构基于 VSCode 插件的 PersistedComposerSessionData，不再使用 config
   */
  async updateSessionConfig(
    sessionId: string,
    _config: Partial<SessionConfig> // 使用下划线前缀表示未使用的参数
  ): Promise<Session | undefined> {
    const session = await this.getSession(sessionId);
    if (!session) return undefined;

    // 新的 Session 结构不再有 config 属性，直接返回会话
    session.updatedAt = Date.now();

    return session;
  }

  /**
   * 检查本地AI服务状态
   */
  async checkLocalServiceStatus(): Promise<{
    isRunning: boolean;
    message: string;
  }> {
    try {
      // 尝试ping本地服务
      const response = await this.localService.request(
        "state/agentState",
        undefined
      );

      if (response.status === "ok") {
        return {
          isRunning: true,
          message: "本地AI服务运行正常",
        };
      } else {
        return {
          isRunning: false,
          message: `本地AI服务状态异常: ${response.status}`,
        };
      }
    } catch (error) {
      this.logger.error("检查本地服务状态失败", error);
      return {
        isRunning: false,
        message: `本地AI服务连接失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats() {
    return {
      totalSessions: this.sessions.size,
      activeSessions: Array.from(this.sessions.values()).filter(
        (session) => Date.now() - session.updatedAt < 24 * 60 * 60 * 1000 // 24小时内活跃
      ).length,
      totalMessages: Array.from(this.sessions.values()).reduce(
        (total, session) => total + session.localMessages.length,
        0
      ),
      localServiceRunning: this.localService.checkIsRunning(),
    };
  }

  /**
   * 推送完整状态更新给前端，参考 VSCode 插件的 postComposerStateUpdate 方法
   */
  private postComposerStateUpdate(
    sessionId: string,
    data: ComposerStateUpdateEvent
  ) {
    this.logger.debug(
      `推送状态更新: ${sessionId}, ${data.localMessages.length}条消息`
    );

    // 通过事件总线发送事件给 WebSocket 网关
    this.eventBus.emitComposerStateUpdate(sessionId, data);
  }

  /**
   * 推送部分消息给前端，参考 VSCode 插件的 bridge.postOneWayMessage 方法
   */
  private postComposerPartialMessage(sessionId: string, partialMessage: any) {
    this.logger.debug(
      `推送部分消息: ${sessionId} - ${partialMessage.text?.slice(0, 50)}...`
    );

    // 通过事件总线发送事件给 WebSocket 网关
    this.eventBus.emitComposerPartialMessage(sessionId, partialMessage);
  }

  /**
   * 从数据库加载会话到内存缓存
   */
  private async loadSessionsFromDatabase(): Promise<void> {
    try {
      const sessions = await this.databaseService.getAllSessions();
      this.sessions.clear();

      for (const session of sessions) {
        // 新的 Session 结构已经包含 localMessages，不需要单独加载消息
        // 直接使用 sessionId 作为键
        this.sessions.set(session.sessionId, session);
      }

      this.logger.log(`从数据库加载了 ${sessions.length} 个会话`);
    } catch (error) {
      this.logger.error(`从数据库加载会话失败: ${error}`);
      // 如果加载失败，继续使用空的会话状态
      this.sessions.clear();
    }
  }

  /**
   * 判断一条 LocalMessage 是否是用户消息，参考 VSCode 插件的 isHumanMessage 实现
   */
  private isHumanMessage(message: any): boolean {
    return (
      message.type === "say" &&
      message.say === "text" &&
      message.role === "user"
    );
  }

  /**
   * 将传统的 Message 转换为 LocalMessage 格式
   */
  private convertMessagesToLocalMessages(
    messages: any[],
    sessionId: string
  ): LocalMessage[] {
    return messages.map((msg) => {
      const localMessage: LocalMessage = {
        ts: msg.ts || msg.createdAt || Date.now(),
        role: msg.role === "user" ? "user" : undefined,
        sessionId,
        type: "say",
        say: "text",
        text: msg.content || msg.text || "",
        partial: msg.partial || false,
      };
      return localMessage;
    });
  }

  /**
   * 智能合并 LocalMessage 列表，参考 VSCode 插件的消息合并逻辑
   */
  private mergeLocalMessages(
    originalMessages: LocalMessage[],
    newMessages: LocalMessage[]
  ): LocalMessage[] {
    const messageMap = new Map<number, LocalMessage>();

    // 先添加原有消息
    for (const msg of originalMessages) {
      messageMap.set(msg.ts, msg);
    }

    // 处理新消息
    for (const newMsg of newMessages) {
      const existingMsg = messageMap.get(newMsg.ts);

      if (existingMsg) {
        // 更新现有消息，保留重要状态
        const updatedMsg: LocalMessage = {
          ...existingMsg,
          text: newMsg.text || existingMsg.text,
          partial:
            newMsg.partial !== undefined ? newMsg.partial : existingMsg.partial,
        };
        messageMap.set(newMsg.ts, updatedMsg);
      } else {
        // 新消息，直接添加
        messageMap.set(newMsg.ts, newMsg);
      }
    }

    // 按时间戳排序返回
    return Array.from(messageMap.values()).sort((a, b) => a.ts - b.ts);
  }

  /**
   * 智能合并消息列表，参考 VSCode 插件的消息合并逻辑 - 已弃用，保留用于向后兼容
   * @deprecated 使用 mergeLocalMessages 替代
   */
  private mergeMessages(
    originalMessages: Message[],
    newMessages: any[]
  ): Message[] {
    const messageMap = new Map<string, Message>();

    // 先添加原有消息
    for (const msg of originalMessages) {
      messageMap.set(msg.id, msg);
    }

    // 处理新消息
    for (const newMsg of newMessages) {
      const msgId = newMsg.ts?.toString() || uuid.v4();
      const existingMsg = messageMap.get(msgId);

      if (existingMsg) {
        // 更新现有消息，保留重要状态
        const updatedMsg: Message = {
          ...existingMsg,
          content:
            newMsg.ask || newMsg.say || newMsg.text || existingMsg.content,
          // 如果是部分消息，可能需要更新内容但保持其他状态
        };
        messageMap.set(msgId, updatedMsg);
      } else {
        // 新消息，根据 VSCode 插件的 isHumanMessage 逻辑判断角色
        const newMessage: Message = {
          id: msgId,
          role: (this.isHumanMessage(newMsg) ? "user" : "assistant") as
            | "user"
            | "assistant"
            | "system",
          content: newMsg.text || "",
          createdAt: newMsg.ts || Date.now(),
        };
        messageMap.set(msgId, newMessage);
      }
    }

    // 按时间戳排序返回
    return Array.from(messageMap.values()).sort(
      (a, b) => a.createdAt - b.createdAt
    );
  }
}
